// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: service_accounts.sql

package database

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const CountActiveClientSecrets = `-- name: CountActiveClientSecrets :one
SELECT COUNT(*) as active_client_secrets 
FROM client_secrets
WHERE service_account_account_id = $1
AND revoked_at IS NOT NULL
`

// used in integration tests
func (q *Queries) CountActiveClientSecrets(ctx context.Context, serviceAccountAccountID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, CountActiveClientSecrets, serviceAccountAccountID)
	var active_client_secrets int64
	err := row.Scan(&active_client_secrets)
	return active_client_secrets, err
}

const CreateClientSecret = `-- name: CreateClientSecret :one
INSERT INTO client_secrets (hashed_secret, service_account_account_id, created_at, updated_at, expires_at)
VALUES ( $1,$2, NOW(), NOW(), $3 )
RETURNING hashed_secret, service_account_account_id
`

type CreateClientSecretParams struct {
	HashedSecret            string    `json:"hashed_secret"`
	ServiceAccountAccountID uuid.UUID `json:"service_account_account_id"`
	ExpiresAt               time.Time `json:"expires_at"`
}

type CreateClientSecretRow struct {
	HashedSecret            string    `json:"hashed_secret"`
	ServiceAccountAccountID uuid.UUID `json:"service_account_account_id"`
}

func (q *Queries) CreateClientSecret(ctx context.Context, arg CreateClientSecretParams) (CreateClientSecretRow, error) {
	row := q.db.QueryRow(ctx, CreateClientSecret, arg.HashedSecret, arg.ServiceAccountAccountID, arg.ExpiresAt)
	var i CreateClientSecretRow
	err := row.Scan(&i.HashedSecret, &i.ServiceAccountAccountID)
	return i, err
}

const CreateOneTimeClientSecret = `-- name: CreateOneTimeClientSecret :one
INSERT INTO one_time_client_secrets (id, service_account_account_id, plaintext_secret, created_at, expires_at)
VALUES ( $1, $2, $3, NOW(), $4)
RETURNING id
`

type CreateOneTimeClientSecretParams struct {
	ID                      uuid.UUID `json:"id"`
	ServiceAccountAccountID uuid.UUID `json:"service_account_account_id"`
	PlaintextSecret         string    `json:"plaintext_secret"`
	ExpiresAt               time.Time `json:"expires_at"`
}

func (q *Queries) CreateOneTimeClientSecret(ctx context.Context, arg CreateOneTimeClientSecretParams) (uuid.UUID, error) {
	row := q.db.QueryRow(ctx, CreateOneTimeClientSecret,
		arg.ID,
		arg.ServiceAccountAccountID,
		arg.PlaintextSecret,
		arg.ExpiresAt,
	)
	var id uuid.UUID
	err := row.Scan(&id)
	return id, err
}

const CreateServiceAccount = `-- name: CreateServiceAccount :one
INSERT INTO service_accounts (
    account_id,
    created_at,
    updated_at,
    client_id,
    client_contact_email,
    client_organization
) VALUES ( $1, NOW(), NOW(), $2, $3, $4)
RETURNING account_id, created_at, updated_at, client_id, client_contact_email, client_organization
`

type CreateServiceAccountParams struct {
	AccountID          uuid.UUID `json:"account_id"`
	ClientID           string    `json:"client_id"`
	ClientContactEmail string    `json:"client_contact_email"`
	ClientOrganization string    `json:"client_organization"`
}

func (q *Queries) CreateServiceAccount(ctx context.Context, arg CreateServiceAccountParams) (ServiceAccount, error) {
	row := q.db.QueryRow(ctx, CreateServiceAccount,
		arg.AccountID,
		arg.ClientID,
		arg.ClientContactEmail,
		arg.ClientOrganization,
	)
	var i ServiceAccount
	err := row.Scan(
		&i.AccountID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ClientID,
		&i.ClientContactEmail,
		&i.ClientOrganization,
	)
	return i, err
}

const DeleteOneTimeClientSecret = `-- name: DeleteOneTimeClientSecret :execrows
DELETE from one_time_client_secrets 
WHERE id = $1
`

func (q *Queries) DeleteOneTimeClientSecret(ctx context.Context, id uuid.UUID) (int64, error) {
	result, err := q.db.Exec(ctx, DeleteOneTimeClientSecret, id)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const DeleteOneTimeClientSecretsByOrgAndEmail = `-- name: DeleteOneTimeClientSecretsByOrgAndEmail :execrows
DELETE from one_time_client_secrets 
WHERE service_account_account_id = (SELECT account_id 
                                    FROM service_accounts 
                                    WHERE client_organization = $1 
                                    AND client_contact_email = $2)
AND expires_at > NOW()
`

type DeleteOneTimeClientSecretsByOrgAndEmailParams struct {
	ClientOrganization string `json:"client_organization"`
	ClientContactEmail string `json:"client_contact_email"`
}

func (q *Queries) DeleteOneTimeClientSecretsByOrgAndEmail(ctx context.Context, arg DeleteOneTimeClientSecretsByOrgAndEmailParams) (int64, error) {
	result, err := q.db.Exec(ctx, DeleteOneTimeClientSecretsByOrgAndEmail, arg.ClientOrganization, arg.ClientContactEmail)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const ExistsServiceAccountWithEmailAndOrganization = `-- name: ExistsServiceAccountWithEmailAndOrganization :one
SELECT EXISTS (
    SELECT 1 FROM service_accounts
    WHERE client_contact_email = $1
    AND client_organization = $2
) AS exists
`

type ExistsServiceAccountWithEmailAndOrganizationParams struct {
	ClientContactEmail string `json:"client_contact_email"`
	ClientOrganization string `json:"client_organization"`
}

func (q *Queries) ExistsServiceAccountWithEmailAndOrganization(ctx context.Context, arg ExistsServiceAccountWithEmailAndOrganizationParams) (bool, error) {
	row := q.db.QueryRow(ctx, ExistsServiceAccountWithEmailAndOrganization, arg.ClientContactEmail, arg.ClientOrganization)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const GetOneTimeClientSecret = `-- name: GetOneTimeClientSecret :one
SELECT created_at, service_account_account_id, plaintext_secret, expires_at
FROM one_time_client_secrets
WHERE id = $1
`

type GetOneTimeClientSecretRow struct {
	CreatedAt               time.Time `json:"created_at"`
	ServiceAccountAccountID uuid.UUID `json:"service_account_account_id"`
	PlaintextSecret         string    `json:"plaintext_secret"`
	ExpiresAt               time.Time `json:"expires_at"`
}

func (q *Queries) GetOneTimeClientSecret(ctx context.Context, id uuid.UUID) (GetOneTimeClientSecretRow, error) {
	row := q.db.QueryRow(ctx, GetOneTimeClientSecret, id)
	var i GetOneTimeClientSecretRow
	err := row.Scan(
		&i.CreatedAt,
		&i.ServiceAccountAccountID,
		&i.PlaintextSecret,
		&i.ExpiresAt,
	)
	return i, err
}

const GetServiceAccountByAccountID = `-- name: GetServiceAccountByAccountID :one
SELECT sa.account_id, sa.created_at, sa.updated_at, sa.client_id, sa.client_contact_email, sa.client_organization FROM service_accounts sa
WHERE sa.account_id = $1
`

func (q *Queries) GetServiceAccountByAccountID(ctx context.Context, accountID uuid.UUID) (ServiceAccount, error) {
	row := q.db.QueryRow(ctx, GetServiceAccountByAccountID, accountID)
	var i ServiceAccount
	err := row.Scan(
		&i.AccountID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ClientID,
		&i.ClientContactEmail,
		&i.ClientOrganization,
	)
	return i, err
}

const GetServiceAccountByClientID = `-- name: GetServiceAccountByClientID :one
SELECT sa.account_id, sa.created_at, sa.updated_at, sa.client_id, sa.client_contact_email, sa.client_organization FROM service_accounts sa
WHERE sa.client_id = $1
`

func (q *Queries) GetServiceAccountByClientID(ctx context.Context, clientID string) (ServiceAccount, error) {
	row := q.db.QueryRow(ctx, GetServiceAccountByClientID, clientID)
	var i ServiceAccount
	err := row.Scan(
		&i.AccountID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ClientID,
		&i.ClientContactEmail,
		&i.ClientOrganization,
	)
	return i, err
}

const GetServiceAccountWithOrganizationAndEmail = `-- name: GetServiceAccountWithOrganizationAndEmail :one
SELECT account_id, created_at, updated_at, client_id, client_contact_email, client_organization FROM service_accounts
    WHERE client_organization = $1
    AND client_contact_email = $2
`

type GetServiceAccountWithOrganizationAndEmailParams struct {
	ClientOrganization string `json:"client_organization"`
	ClientContactEmail string `json:"client_contact_email"`
}

func (q *Queries) GetServiceAccountWithOrganizationAndEmail(ctx context.Context, arg GetServiceAccountWithOrganizationAndEmailParams) (ServiceAccount, error) {
	row := q.db.QueryRow(ctx, GetServiceAccountWithOrganizationAndEmail, arg.ClientOrganization, arg.ClientContactEmail)
	var i ServiceAccount
	err := row.Scan(
		&i.AccountID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ClientID,
		&i.ClientContactEmail,
		&i.ClientOrganization,
	)
	return i, err
}

const GetServiceAccounts = `-- name: GetServiceAccounts :many
SELECT sa.account_id, sa.created_at, sa.updated_at, sa.client_id, sa.client_contact_email, sa.client_organization 
FROM service_accounts sa
`

func (q *Queries) GetServiceAccounts(ctx context.Context) ([]ServiceAccount, error) {
	rows, err := q.db.Query(ctx, GetServiceAccounts)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ServiceAccount
	for rows.Next() {
		var i ServiceAccount
		if err := rows.Scan(
			&i.AccountID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.ClientID,
			&i.ClientContactEmail,
			&i.ClientOrganization,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetValidClientSecretByHashedSecret = `-- name: GetValidClientSecretByHashedSecret :one
SELECT hashed_secret, created_at, updated_at, service_account_account_id, expires_at, revoked_at FROM client_secrets
WHERE hashed_secret = $1
AND revoked_at IS NULL
AND expires_at > NOW()
`

func (q *Queries) GetValidClientSecretByHashedSecret(ctx context.Context, hashedSecret string) (ClientSecret, error) {
	row := q.db.QueryRow(ctx, GetValidClientSecretByHashedSecret, hashedSecret)
	var i ClientSecret
	err := row.Scan(
		&i.HashedSecret,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.ServiceAccountAccountID,
		&i.ExpiresAt,
		&i.RevokedAt,
	)
	return i, err
}

const GetValidClientSecretByServiceAccountAccountId = `-- name: GetValidClientSecretByServiceAccountAccountId :one
SELECT hashed_secret, expires_at
FROM client_secrets
WHERE service_account_account_id = $1
  AND revoked_at IS NULL
  AND expires_at > NOW()
`

type GetValidClientSecretByServiceAccountAccountIdRow struct {
	HashedSecret string    `json:"hashed_secret"`
	ExpiresAt    time.Time `json:"expires_at"`
}

func (q *Queries) GetValidClientSecretByServiceAccountAccountId(ctx context.Context, serviceAccountAccountID uuid.UUID) (GetValidClientSecretByServiceAccountAccountIdRow, error) {
	row := q.db.QueryRow(ctx, GetValidClientSecretByServiceAccountAccountId, serviceAccountAccountID)
	var i GetValidClientSecretByServiceAccountAccountIdRow
	err := row.Scan(&i.HashedSecret, &i.ExpiresAt)
	return i, err
}

const RevokeAllClientSecretsForAccount = `-- name: RevokeAllClientSecretsForAccount :execrows
UPDATE client_secrets SET (updated_at, revoked_at) = (NOW(), NOW())
WHERE service_account_account_id = $1
AND revoked_at IS NULL
`

func (q *Queries) RevokeAllClientSecretsForAccount(ctx context.Context, serviceAccountAccountID uuid.UUID) (int64, error) {
	result, err := q.db.Exec(ctx, RevokeAllClientSecretsForAccount, serviceAccountAccountID)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const RevokeClientSecret = `-- name: RevokeClientSecret :execrows
UPDATE client_secrets SET (updated_at, revoked_at) = (NOW(), NOW()) 
WHERE hashed_secret = $1
`

func (q *Queries) RevokeClientSecret(ctx context.Context, hashedSecret string) (int64, error) {
	result, err := q.db.Exec(ctx, RevokeClientSecret, hashedSecret)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}

const ScheduleRevokeAllClientSecretsForAccount = `-- name: ScheduleRevokeAllClientSecretsForAccount :execrows
UPDATE client_secrets SET (updated_at, revoked_at) = (NOW() + INTERVAL '5 minutes', NOW())
WHERE service_account_account_id = $1
AND revoked_at IS NULL
`

func (q *Queries) ScheduleRevokeAllClientSecretsForAccount(ctx context.Context, serviceAccountAccountID uuid.UUID) (int64, error) {
	result, err := q.db.Exec(ctx, ScheduleRevokeAllClientSecretsForAccount, serviceAccountAccountID)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected(), nil
}
